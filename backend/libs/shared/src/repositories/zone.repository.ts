import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma/prisma.service';
import { Zone } from './models/zone.model';
import {
  CreateZoneInputDto,
  UpdateZoneInputDto,
} from '../modules/zone/zone-input.dto';
import { H3UtilityService } from '../common/h3-utility/h3-utility.service';

@Injectable()
export class ZoneRepository {
  constructor(
    private readonly prisma: PrismaService,
    private readonly h3UtilityService: H3UtilityService,
  ) { }

  /**
   * Create a new zone with H3 indexing (using DTOs and proper GeoJSON validation like city service)
   */
  async create(data: CreateZoneInputDto): Promise<Zone> {
    let geoJsonPolygon = null;
    // Convert GeoJSON geometry to proper polygon format if provided
    if (data.polygon && data.polygon.length > 0) {
      this.h3UtilityService.validateCoordinates(data.polygon);

      geoJsonPolygon = this.h3UtilityService.coordinatesToGeoJsonPolygon(
        data.polygon,
      );
    }
    // console.log('geoJsonPolygon:', geoJsonPolygon);

    const h3Indexes = geoJsonPolygon
      ? this.h3UtilityService.polygonToH3Indexes(geoJsonPolygon, 8)
      : [];

    // Create zone and H3 index entries in a transaction
    const result = await this.prisma.$transaction(async (tx) => {
      // Create the zone
      const zone = await tx.zone.create({
        data: {
          name: data.name,
          description: data.description ?? null,
          polygon: (data.polygon ?? null) as any, // Cast to satisfy Prisma JSON type
          meta: (data.meta ?? null) as any,
          zoneTypeId: data.zoneTypeId,
          cityId: data.cityId,
          isCity: false,
          h3Indexes: h3Indexes,
          priority: data.priority || 100,
        },
      });

      // Create H3 index to zone mappings only if we have indexes
      if (h3Indexes.length > 0) {
        const h3IndexData = h3Indexes.map((index) => ({
          h3Index: BigInt(`0x${index}`),
          zoneId: zone.id,
        }));

        await tx.h3IndexToZone.createMany({
          data: h3IndexData,
          skipDuplicates: true,
        });
      }

      return zone;
    });

    return this.mapToModel(result);
  }

  /**
   * Find all zones with optional filters
   */
  async findAll(options?: {
    cityId?: string;
    zoneTypeId?: string;
    includeDeleted?: boolean;
    includeRelations?: boolean;
  }): Promise<Zone[]> {
    const where: any = {
      isCity: false,
    };

    if (options?.cityId) {
      where.cityId = options.cityId;
    }

    if (options?.zoneTypeId) {
      where.zoneTypeId = options.zoneTypeId;
    }

    if (!options?.includeDeleted) {
      where.deletedAt = null;
    }

    const include: any = {};
    if (options?.includeRelations) {
      include.zoneType = {
        select: {
          id: true,
          name: true,
          algorithm: true,
        },
      };
      include.city = {
        select: {
          id: true,
          name: true,
        },
      };
    }

    const zones = await this.prisma.zone.findMany({
      where,
      include,
      orderBy: {
        createdAt: 'desc',
      },
    });

    return zones.map((zone: any) => this.mapToModel(zone));
  }

  /**
   * Find zone by ID
   */
  async findById(
    id: string,
    options?: {
      includeRelations?: boolean;
      includeDeleted?: boolean;
    },
  ): Promise<Zone | null> {
    const where: any = {
      id,
      isCity: false,
    };

    if (!options?.includeDeleted) {
      where.deletedAt = null;
    }

    const include: any = {};
    if (options?.includeRelations) {
      include.zoneType = {
        select: {
          id: true,
          name: true,
          algorithm: true,
        },
      };
      include.city = {
        select: {
          id: true,
          name: true,
        },
      };
    }

    const zone = await this.prisma.zone.findFirst({
      where,
      include,
    });

    return zone ? this.mapToModel(zone) : null;
  }

  /**
   * Find zone by name within a city
   */
  async findByName(name: string, cityId?: string): Promise<Zone | null> {
    const where: any = {
      name,
      isCity: false,
      deletedAt: null,
    };

    if (cityId) {
      where.cityId = cityId;
    }

    const zone = await this.prisma.zone.findFirst({
      where,
    });

    return zone ? this.mapToModel(zone) : null;
  }

  /**
   * Find zones by city ID (excludes city zones - isCity: false only)
   */
  async findByCityId(
    cityId: string,
    options?: { includeRelations?: boolean },
  ): Promise<Zone[]> {
    const include: any = {};
    if (options?.includeRelations) {
      include.zoneType = {
        select: {
          id: true,
          name: true,
          algorithm: true,
        },
      };
    }

    const zones = await this.prisma.zone.findMany({
      where: {
        cityId,
        isCity: false,
        deletedAt: null,
      },
      include,
      orderBy: {
        createdAt: 'desc',
      },
    });

    return zones.map((zone) => this.mapToModel(zone));
  }

  /**
   * Find all zones by city ID (includes both city zones and regular zones)
   */
  async findAllZonesByCityId(
    cityId: string,
    options?: { includeRelations?: boolean },
  ): Promise<Zone[]> {
    const include: any = {};
    if (options?.includeRelations) {
      include.zoneType = {
        select: {
          id: true,
          name: true,
          algorithm: true,
        },
      };
      include.city = {
        select: {
          id: true,
          name: true,
        },
      };
    }

    const zones = await this.prisma.zone.findMany({
      where: {
        cityId,
        deletedAt: null,
      },
      include,
      orderBy: [
        { isCity: 'desc' }, // City zones first
        { createdAt: 'desc' },
      ],
    });

    return zones.map((zone) => this.mapToModel(zone));
  }

  /**
   * Find all zones by multiple city IDs (includes both city zones and regular zones)
   */
  async findAllZonesByMultipleCityIds(
    cityIds: string[],
    options?: { includeRelations?: boolean },
  ): Promise<Zone[]> {
    if (!cityIds || cityIds.length === 0) {
      return [];
    }

    const include: any = {};
    if (options?.includeRelations) {
      include.zoneType = {
        select: {
          id: true,
          name: true,
          algorithm: true,
        },
      };
      include.city = {
        select: {
          id: true,
          name: true,
        },
      };
    }

    const zones = await this.prisma.zone.findMany({
      where: {
        cityId: {
          in: cityIds,
        },
        deletedAt: null,
      },
      include,
      orderBy: [
        { cityId: 'asc' }, // Group by city
        { isCity: 'desc' }, // City zones first within each city
        { createdAt: 'desc' },
      ],
    });

    return zones.map((zone) => this.mapToModel(zone));
  }

  /**
   * Find zones by zone type ID
   */
  async findByZoneTypeId(zoneTypeId: string): Promise<Zone[]> {
    const zones = await this.prisma.zone.findMany({
      where: {
        zoneTypeId,
        isCity: false,
        deletedAt: null,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return zones.map((zone) => this.mapToModel(zone));
  }

  /**
   * Update zone by ID with H3 index management
   */
  async update(id: string, data: UpdateZoneInputDto): Promise<Zone | null> {
    try {
      const result = await this.prisma.$transaction(async (tx) => {
        // Get current zone to check if polygon changed
        const currentZone = await tx.zone.findUnique({
          where: { id },
          select: { polygon: true, h3Indexes: true },
        });

        if (!currentZone) {
          return null;
        }

        // Prepare update data
        const updateData: any = {};
        let newH3Indexes: string[] | undefined;

        if (data.name !== undefined) updateData.name = data.name;
        if (data.description !== undefined)
          updateData.description = data.description;
        if (data.meta !== undefined) updateData.meta = data.meta as any;
        if (data.priority !== undefined) updateData.priority = data.priority;
        if (data.zoneTypeId !== undefined)
          updateData.zoneTypeId = data.zoneTypeId;

        // Handle polygon updates
        if (data.polygon !== undefined) {
          updateData.polygon = data.polygon as any; // Cast to satisfy Prisma JSON type
          // Generate H3 indexes only if polygon is provided (not null)
          if (data.polygon) {
            let geoJsonPolygon = null;
            // Convert GeoJSON geometry to proper polygon format if provided
            if (data.polygon && data.polygon.length > 0) {
              this.h3UtilityService.validateCoordinates(data.polygon);

              geoJsonPolygon =
                this.h3UtilityService.coordinatesToGeoJsonPolygon(data.polygon);
            }
            newH3Indexes = this.h3UtilityService.polygonToH3Indexes(
              geoJsonPolygon as any,
              8,
            );
          } else {
            newH3Indexes = []; // Clear H3 indexes if polygon is set to null
          }
          // updateData.h3Indexes = newH3Indexes;
        }

        // Update the zone
        const updatedZone = await tx.zone.update({
          where: { id },
          data: updateData,
        });

        // Update H3 index mappings if polygon changed
        if (newH3Indexes !== undefined) {
          // Delete old H3 index mappings
          await tx.h3IndexToZone.deleteMany({
            where: { zoneId: id },
          });

          // Create new H3 index mappings
          if (newH3Indexes.length > 0) {
            const h3IndexData = newH3Indexes.map((index) => ({
              h3Index: BigInt(`0x${index}`),
              zoneId: id,
            }));

            await tx.h3IndexToZone.createMany({
              data: h3IndexData,
              skipDuplicates: true,
            });
          }
        }

        return updatedZone;
      });

      return result ? this.mapToModel(result) : null;
    } catch (error: any) {
      if (error.code === 'P2025') {
        return null;
      }
      throw error;
    }
  }

  async priorityExists(priority: number, cityId: string): Promise<Zone | null> {
    const where: any = {
      priority,
      cityId,
      isCity: false,
      deletedAt: null,
    };

    const zone = await this.prisma.zone.findFirst({
      where,
    });

    return zone ? this.mapToModel(zone) : null;
  }

  /**
   * Soft delete zone by ID
   */
  async softDelete(id: string): Promise<Zone | null> {
    try {
      const result = await this.prisma.$transaction(async (tx) => {
        // Soft delete the zone
        const deletedZone = await tx.zone.update({
          where: { id, isCity: false },
          data: {
            deletedAt: new Date(),
          },
        });

        // Delete H3 index mappings
        await tx.h3IndexToZone.deleteMany({
          where: { zoneId: id },
        });

        return deletedZone;
      });

      return this.mapToModel(result);
    } catch (error: any) {
      if (error.code === 'P2025') {
        return null;
      }
      throw error;
    }
  }

  /**
   * Hard delete zone by ID (use with caution)
   */
  async hardDelete(id: string): Promise<boolean> {
    try {
      await this.prisma.$transaction(async (tx) => {
        // Delete H3 index mappings first
        await tx.h3IndexToZone.deleteMany({
          where: { zoneId: id },
        });

        // Delete the zone
        await tx.zone.delete({
          where: { id, isCity: false },
        });
      });

      return true;
    } catch (error: any) {
      if (error.code === 'P2025') {
        return false;
      }
      throw error;
    }
  }

  /**
   * Get paginated zones
   */
  async findPaginated(
    page: number = 1,
    limit: number = 10,
    filters?: {
      search?: string;
      cityId?: string;
      zoneTypeId?: string;
      includeRelations?: boolean;
    },
  ): Promise<{
    data: Zone[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  }> {
    const offset = (page - 1) * limit;
    const where: any = {
      isCity: false,
      deletedAt: null,
    };

    // Build filters
    if (filters?.cityId) {
      where.cityId = filters.cityId;
    }

    if (filters?.zoneTypeId) {
      where.zoneTypeId = filters.zoneTypeId;
    }

    if (filters?.search) {
      where.name = {
        contains: filters.search,
        mode: 'insensitive',
      };
    }

    const include: any = {};
    if (filters?.includeRelations) {
      include.zoneType = {
        select: {
          id: true,
          name: true,
          algorithm: true,
        },
      };
    }

    // Execute queries
    const [zones, total] = await Promise.all([
      this.prisma.zone.findMany({
        where,
        include,
        skip: offset,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.zone.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: zones.map((zone) => this.mapToModel(zone)),
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * Check if zone name exists in city
   */
  async nameExists(
    name: string,
    cityId: string,
    excludeId?: string,
  ): Promise<boolean> {
    const where: any = {
      name: {
        equals: name,
        mode: 'insensitive',
      },
      cityId,
      isCity: false,
      deletedAt: null,
    };

    if (excludeId) {
      where.id = {
        not: excludeId,
      };
    }

    const count = await this.prisma.zone.count({ where });
    return count > 0;
  }

  /**
   * Find zones by H3 index
   */
  async findByH3Index(h3Index: string): Promise<Zone[]> {
    // Convert H3 index string to BigInt for lookup
    const h3BigInt = BigInt(`0x${h3Index}`);

    const h3Mappings = await this.prisma.h3IndexToZone.findMany({
      where: { h3Index: h3BigInt },
      include: {
        zone: true,
      },
    });

    return h3Mappings
      .filter(
        (mapping) =>
          mapping.zone &&
          mapping.zone.isCity === false &&
          mapping.zone.deletedAt === null,
      )
      .map((mapping) => this.mapToModel(mapping.zone!));
  }

  /**
   * Restore soft-deleted zone
   */
  async restore(id: string): Promise<Zone | null> {
    try {
      const result = await this.prisma.$transaction(async (tx) => {
        // Get the zone to restore
        const zone = await tx.zone.findUnique({
          where: { id, isCity: false },
          select: { h3Indexes: true, polygon: true },
        });

        if (!zone) {
          return null;
        }

        // Restore the zone
        const restoredZone = await tx.zone.update({
          where: { id },
          data: {
            deletedAt: null,
          },
        });

        // Recreate H3 index mappings
        if (zone.h3Indexes && zone.h3Indexes.length > 0) {
          const h3IndexData = zone.h3Indexes.map((index) => ({
            h3Index: BigInt(`0x${index}`),
            zoneId: id,
          }));

          await tx.h3IndexToZone.createMany({
            data: h3IndexData,
            skipDuplicates: true,
          });
        }

        return restoredZone;
      });

      return result ? this.mapToModel(result) : null;
    } catch (error: any) {
      if (error.code === 'P2025') {
        return null;
      }
      throw error;
    }
  }

  // ===== LEGACY FUNCTIONS (used by zone service legacy methods) =====

  /**
   * Create legacy zone (for backward compatibility)
   */
  async createLegacyZone(
    data: Omit<Zone, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Zone> {
    const result = await this.prisma.zone.create({
      data: {
        name: data.name,
        description: data.description ?? null,
        polygon: data.polygon as any,
        meta: (data.meta ?? null) as any,
        priority: data.priority ?? 100,
        zoneTypeId: data.zoneTypeId ?? null,
        cityId: data.cityId ?? null,
        isCity: data.isCity || false,
        h3Indexes: data.h3Indexes || [],
      },
    });

    return this.mapToModel(result);
  }

  /**
   * Create zone (legacy function)
   */
  async createZone(
    data: Omit<Zone, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Zone> {
    return this.createLegacyZone(data);
  }

  /**
   * Find all zones (legacy function)
   */
  async findAllZones(): Promise<Zone[]> {
    const zones = await this.prisma.zone.findMany({
      where: {
        deletedAt: null,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return zones.map((zone) => this.mapToModel(zone));
  }

  /**
   * Find zone by ID (legacy function)
   */
  async findZoneById(id: string): Promise<Zone | null> {
    const zone = await this.prisma.zone.findFirst({
      where: {
        id,
        deletedAt: null,
      },
    });

    return zone ? this.mapToModel(zone) : null;
  }

  /**
   * Find zones by city (legacy function)
   */
  async findZonesByCity(cityId: string): Promise<Zone[]> {
    const zones = await this.prisma.zone.findMany({
      where: {
        cityId,
        deletedAt: null,
        isCity: false,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return zones.map((zone) => this.mapToModel(zone));
  }

  /**
   * Find city zone (legacy function)
   */
  async findCityZone(cityId: string): Promise<Zone | null> {
    const zone = await this.prisma.zone.findFirst({
      where: {
        OR: [
          { cityId: cityId, isCity: true },
          { name: { contains: cityId }, isCity: true }, // Fallback for city name matching
        ],
        deletedAt: null,
      },
    });

    return zone ? this.mapToModel(zone) : null;
  }

  /**
   * Find zones by H3 index (legacy function)
   */
  async findZonesByH3Index(h3Index: string): Promise<Zone[]> {
    // Convert H3 index string to BigInt for lookup
    const h3BigInt = BigInt(`0x${h3Index}`);

    // First try to find via H3 index to zone mappings
    const h3Mappings = await this.prisma.h3IndexToZone.findMany({
      where: { h3Index: h3BigInt },
      include: {
        zone: true,
      },
    });

    const zones = h3Mappings
      .filter((mapping) => mapping.zone && mapping.zone.deletedAt === null)
      .map((mapping) => this.mapToModel(mapping.zone!));

    // Also search in h3Indexes array directly (legacy support)
    const directZones = await this.prisma.zone.findMany({
      where: {
        h3Indexes: {
          has: h3Index,
        },
        deletedAt: null,
      },
    });

    const directZonesModels = directZones.map((zone) => this.mapToModel(zone));

    // Combine and deduplicate
    const allZones = [...zones, ...directZonesModels];
    const uniqueZones = allZones.filter(
      (zone, index, self) => index === self.findIndex((z) => z.id === zone.id),
    );

    return uniqueZones;
  }

  /**
   * Update zone (legacy function)
   */
  async updateZone(id: string, data: Partial<Zone>): Promise<Zone> {
    const result = await this.prisma.zone.update({
      where: { id },
      data: {
        ...(data.name !== undefined && { name: data.name }),
        ...(data.description !== undefined && {
          description: data.description,
        }),
        ...(data.polygon !== undefined && { polygon: data.polygon as any }),
        ...(data.meta !== undefined && { meta: data.meta as any }),
        ...(data.priority !== undefined && { priority: data.priority }),
        ...(data.zoneTypeId !== undefined && { zoneTypeId: data.zoneTypeId }),
        ...(data.cityId !== undefined && { cityId: data.cityId }),
        ...(data.isCity !== undefined && { isCity: data.isCity }),
        ...(data.h3Indexes !== undefined && { h3Indexes: data.h3Indexes }),
      },
    });

    return this.mapToModel(result);
  }

  /**
   * Delete zone (legacy function)
   */
  async deleteZone(id: string): Promise<Zone> {
    const result = await this.prisma.zone.update({
      where: { id },
      data: {
        deletedAt: new Date(),
      },
    });

    return this.mapToModel(result);
  }

  /**
   * Paginate zones (legacy function)
   */
  async paginateZones(
    page: number,
    limit: number,
    options?: any,
  ): Promise<any> {
    const offset = (page - 1) * limit;
    const where = options?.where || { deletedAt: null };

    const [zones, total] = await Promise.all([
      this.prisma.zone.findMany({
        where,
        skip: offset,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.zone.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: zones.map((zone) => this.mapToModel(zone)),
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * Map Prisma model to Zone interface
   */
  private mapToModel(prismaZone: any): Zone {
    return {
      id: prismaZone.id,
      name: prismaZone.name,
      description: prismaZone.description,
      polygon: prismaZone.polygon,
      meta: prismaZone.meta,
      priority: prismaZone.priority,
      zoneTypeId: prismaZone.zoneTypeId,
      cityId: prismaZone.cityId,
      isCity: prismaZone.isCity,
      h3Indexes: prismaZone.h3Indexes || [],
      createdAt: prismaZone.createdAt,
      updatedAt: prismaZone.updatedAt,
      deletedAt: prismaZone.deletedAt,
      // Include relations if they exist
      ...(prismaZone.zoneType && { zoneType: prismaZone.zoneType }),
      ...(prismaZone.h3IndexToZones && {
        h3IndexToZones: prismaZone.h3IndexToZones,
      }),
    };
  }
}
