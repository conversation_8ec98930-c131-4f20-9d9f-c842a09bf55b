import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export interface ZoneResponseDto {
  id: string;
  name: string;
  description?: string | null;
  polygon?: any; // GeoJSON polygon (optional)
  meta?: any; // JSON metadata
  priority?: number; // Priority for zone selection (lower value = higher precedence)
  zoneTypeId?: string | null;
  cityId?: string | null;
  isCity: boolean;
  h3Indexes: string[];
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;

  // Relations
  zoneType?: {
    id: string;
    name: string;
    algorithm: string;
  };
}

export interface PaginatedZoneResponseDto {
  data: ZoneResponseDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export class ZoneResponseDtoClass {
  @ApiProperty({
    description: 'Unique identifier for the zone',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id!: string;

  @ApiProperty({
    description: 'Name of the zone',
    example: 'Downtown Business District',
  })
  name!: string;

  @ApiPropertyOptional({
    description: 'Description of the zone',
    example: 'A bustling commercial area in the heart of the city',
  })
  description?: string | null;

  @ApiPropertyOptional({
    description:
      'GeoJSON polygon representing the zone boundaries (optional - zones can exist without geographic boundaries)',
    example: [
      { lat: 37.81331899998324, lng: -122.40898669999721 },
      { lat: 37.78663020000072, lng: -122.3805436999997 },
      { lat: 37.71980619999785, lng: -122.35447369999936 },
      { lat: 37.70761319999757, lng: -122.5123436999984 },
      { lat: 37.78358719999717, lng: -122.5247187000022 },
      { lat: 37.815157199999845, lng: -122.4798767000009 },
    ],
  })
  polygon?: any;

  @ApiPropertyOptional({
    description: 'Additional metadata for the zone',
    example: {
      priority: 'high',
      tags: ['commercial', 'busy', 'corporate'],
      capacity: 1000,
      features: ['parking', 'public_transport', 'restaurants', 'hotels'],
      operationalHours: {
        weekdays: { start: '06:00', end: '23:00' },
        weekends: { start: '08:00', end: '22:00' },
      },
      pricing: {
        baseMultiplier: 1.2,
        surgeThreshold: 0.8,
        maxSurge: 2.5,
      },
    },
  })
  meta?: any;

  @ApiPropertyOptional({
    description: 'Zone type identifier',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  zoneTypeId?: string | null;

  @ApiPropertyOptional({
    description: 'City identifier',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  cityId?: string | null;

  @ApiProperty({
    description: 'Whether this is a city zone (true for city zones, false for regular zones)',
    example: false,
  })
  isCity!: boolean;

  @ApiPropertyOptional({
    description:
      'Priority for zone selection when multiple zones overlap (lower value = higher precedence)',
    example: 10,
  })
  priority?: number;

  @ApiProperty({
    description:
      'H3 indexes covering this zone for geographic queries (generated at resolution 9)',
    example: [
      '8a2a1072b59ffff',
      '8a2a1072b5bffff',
      '8a2a1072b43ffff',
      '8a2a1072b47ffff',
      '8a2a1072b4bffff',
      '8a2a1072b53ffff',
    ],
    type: [String],
  })
  h3Indexes!: string[];

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt!: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T14:45:00.000Z',
  })
  updatedAt!: Date;

  @ApiPropertyOptional({
    description: 'Deletion timestamp (if soft deleted)',
    example: null,
  })
  deletedAt?: Date | null;

  @ApiPropertyOptional({
    description: 'Zone type details (included when includeRelations=true)',
    example: {
      id: '456e7890-e12b-34d5-a678-901234567def',
      name: 'Commercial City Zone',
      algorithm: 'CITY',
    },
  })
  zoneType?: {
    id: string;
    name: string;
    algorithm: string;
  };
}
